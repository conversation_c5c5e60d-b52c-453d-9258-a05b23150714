"""
Authentication dialog for user verification
"""
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QPushButton, QMessageBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont
from utils.validation_utils import validate_username
from utils.resource_utils import get_application_icon


class AuthDialog(QDialog):
    """
    Authentication dialog that validates user credentials
    """
    authenticated = pyqtSignal(str)  # username

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("开始使用")
        self.setModal(True)
        # Increased width to accommodate wider input field and buttons
        self.setFixedSize(420, 180)
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.CustomizeWindowHint)

        # Set dialog icon with robust loading and fallback
        self.load_dialog_icon()

        self.setup_ui()

    def load_dialog_icon(self):
        """
        Load dialog icon using base64 data with multiple fallback methods
        """
        icon_loaded = False

        # Method 1: Try base64 encoded icon
        try:
            icon = get_application_icon()
            if icon is not None:
                self.setWindowIcon(icon)
                icon_loaded = True
        except Exception:
            pass

        # Method 2: Fallback to system icon if base64 loading failed
        if not icon_loaded:
            try:
                # Use a standard system icon as fallback
                system_icon = self.style().standardIcon(self.style().SP_ComputerIcon)
                if not system_icon.isNull():
                    self.setWindowIcon(system_icon)
                    icon_loaded = True
            except Exception:
                pass

        # Method 3: Force icon display (Windows-specific workaround)
        if icon_loaded:
            try:
                # Ensure the window shows the icon by setting window flags
                current_flags = self.windowFlags()
                self.setWindowFlags(current_flags | Qt.WindowSystemMenuHint)
            except Exception:
                pass

        return icon_loaded

    def calculate_responsive_widths(self):
        """
        Calculate responsive widths based on dialog size and content
        """
        # Base width calculation: dialog width minus margins and padding
        available_width = self.width() - 60  # 30px margins on each side

        # Minimum width to ensure text label fits comfortably
        min_input_width = 240  # Minimum for "请输入使用人姓名" (8 characters) + padding

        # Use the larger of available width or minimum width
        input_width = max(min_input_width, int(available_width * 0.7))

        # Button width calculation (2 buttons with spacing)
        button_spacing = 15
        button_width = (input_width - button_spacing) // 2

        return input_width, button_width

    def setup_ui(self):
        """
        Setup the user interface with modern styling and enhanced layout
        """
        layout = QVBoxLayout()
        layout.setSpacing(10)
        layout.setContentsMargins(30, 20, 30, 20)

        # Title section without icon (icon removed as requested)
        title_layout = QHBoxLayout()
        title_layout.setAlignment(Qt.AlignCenter)

        # Title label only (icon removed)
        title_label = QLabel("请输入使用人姓名")
        title_font = QFont()
        title_font.setBold(True)
        title_font.setPointSize(12)
        title_label.setFont(title_font)
        title_layout.addWidget(title_label)

        layout.addLayout(title_layout)

        # Reduced spacing
        layout.addSpacing(8)

        # Username input with responsive width (longer than text label)
        input_layout = QHBoxLayout()
        input_layout.addStretch(1)

        # Calculate responsive widths
        input_width, button_width = self.calculate_responsive_widths()

        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("请输入姓名")
        # Extended width to be longer than the text label "请输入使用人姓名" (8 characters)
        self.username_input.setFixedWidth(input_width)
        self.username_input.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 2px solid #e0e0e0;
                border-radius: 6px;
                font-size: 11pt;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
                outline: none;
            }
        """)
        self.username_input.returnPressed.connect(self.submit)
        input_layout.addWidget(self.username_input)
        input_layout.addStretch(1)

        layout.addLayout(input_layout)

        # Reduced spacing
        layout.addSpacing(15)

        # Modern styled buttons with responsive width to match input field
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)
        button_layout.setAlignment(Qt.AlignCenter)

        # Button width already calculated in responsive width calculation

        self.submit_btn = QPushButton("提交")
        self.submit_btn.setFixedSize(button_width, 35)  # Extended button width
        self.submit_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 10pt;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        self.submit_btn.clicked.connect(self.submit)
        button_layout.addWidget(self.submit_btn)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setFixedSize(button_width, 35)  # Extended button width
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #f5f5f5;
                color: #666;
                border: 1px solid #ddd;
                border-radius: 6px;
                font-size: 10pt;
            }
            QPushButton:hover {
                background-color: #e8e8e8;
                border-color: #ccc;
            }
            QPushButton:pressed {
                background-color: #ddd;
            }
        """)
        self.cancel_btn.clicked.connect(self.cancel)
        button_layout.addWidget(self.cancel_btn)

        layout.addLayout(button_layout)

        self.setLayout(layout)

        # Focus on input
        self.username_input.setFocus()

    def submit(self):
        """
        Handle submit button click - Fixed to show only one error dialog
        """
        username = self.username_input.text().strip()

        if not username:
            QMessageBox.warning(self, "警告", "请输入使用人姓名")
            return

        if validate_username(username):
            self.authenticated.emit(username)
            self.accept()
        else:
            # Show single error dialog and immediately exit application
            QMessageBox.critical(self, "权限错误", "您没有权限进行此操作")
            import sys
            sys.exit()  # Immediately terminate application

    def cancel(self):
        """
        Handle cancel button click
        """
        import sys
        sys.exit()  # Immediately terminate application

    def closeEvent(self, event):
        """
        Handle dialog close event
        """
        import sys
        event.accept()  # Accept the close event
        sys.exit()  # Immediately terminate application
