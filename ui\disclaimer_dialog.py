"""
Disclaimer dialogs for different functionalities
"""
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                            QPushButton, QScrollArea, QFrame)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont
from utils.resource_utils import get_application_icon


class DisclaimerDialog(QDialog):
    """
    Generic disclaimer dialog with modern styling
    """
    accepted = pyqtSignal()
    rejected = pyqtSignal()

    def __init__(self, title, content, parent=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        self.setModal(True)
        # Enhanced dialog size for better proportions and responsive layout
        self.setFixedSize(700, 480)
        self.setWindowFlags(Qt.Dialog | Qt.WindowTitleHint | Qt.CustomizeWindowHint)

        # Enhanced dialog icon loading with robust fallback system
        self.load_dialog_icon()

        self.setup_ui(content)

    def load_dialog_icon(self):
        """
        Load dialog icon using base64 data with robust fallback methods
        """
        icon_loaded = False

        # Method 1: Try base64 encoded icon
        try:
            icon = get_application_icon()
            if icon is not None:
                self.setWindowIcon(icon)
                icon_loaded = True
        except Exception:
            pass

        # Method 2: Fallback to system icon if base64 loading failed
        if not icon_loaded:
            try:
                # Use a standard system icon as fallback
                system_icon = self.style().standardIcon(self.style().SP_ComputerIcon)
                if not system_icon.isNull():
                    self.setWindowIcon(system_icon)
                    icon_loaded = True
            except Exception:
                pass

        # Method 3: Force icon display (Windows-specific workaround)
        if icon_loaded:
            try:
                # Ensure the window shows the icon by setting window flags
                current_flags = self.windowFlags()
                self.setWindowFlags(current_flags | Qt.WindowSystemMenuHint)
            except Exception:
                pass

        return icon_loaded

    def calculate_responsive_widths(self):
        """
        Calculate responsive widths based on dialog size and content
        """
        # Base width calculation: dialog width minus margins and padding
        available_width = self.width() - 50  # 25px margins on each side

        # Calculate button widths for responsive layout
        accept_btn_min_width = 140  # Minimum for "我已知晓并同意"
        reject_btn_min_width = 100  # Minimum for "退出程序"
        button_spacing = 15

        # Use responsive sizing while maintaining minimum widths
        total_min_width = accept_btn_min_width + reject_btn_min_width + button_spacing
        if available_width > total_min_width:
            # Scale buttons proportionally
            scale_factor = min(1.2, available_width / total_min_width)
            accept_btn_width = int(accept_btn_min_width * scale_factor)
            reject_btn_width = int(reject_btn_min_width * scale_factor)
        else:
            accept_btn_width = accept_btn_min_width
            reject_btn_width = reject_btn_min_width

        return accept_btn_width, reject_btn_width

    def setup_ui(self, content):
        """
        Setup the user interface with modern styling and enhanced layout
        """
        layout = QVBoxLayout()
        layout.setSpacing(15)
        layout.setContentsMargins(25, 20, 25, 20)

        # Header without icon (icon removed as requested for minimalist design)
        header_layout = QHBoxLayout()
        header_layout.setAlignment(Qt.AlignCenter)

        # Title only (icon removed for clean minimalist appearance)
        title_label = QLabel("免责声明")
        title_font = QFont()
        title_font.setBold(True)
        title_font.setPointSize(14)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: #333;")
        header_layout.addWidget(title_label)

        layout.addLayout(header_layout)

        # Content area with better styling
        content_frame = QFrame()
        content_frame.setFrameStyle(QFrame.Box)
        content_frame.setStyleSheet("""
            QFrame {
                border: 1px solid #ddd;
                border-radius: 8px;
                background-color: #fafafa;
                padding: 15px;
            }
        """)

        content_layout = QVBoxLayout(content_frame)

        # Content text with better typography
        content_label = QLabel(content)
        content_label.setWordWrap(True)
        content_label.setAlignment(Qt.AlignTop | Qt.AlignLeft)

        # Improved font and styling
        content_font = QFont()
        content_font.setPointSize(11)
        content_font.setFamily("Microsoft YaHei")
        content_label.setFont(content_font)
        content_label.setStyleSheet("""
            QLabel {
                color: #444;
                line-height: 1.6;
                padding: 10px;
                background-color: transparent;
            }
        """)

        # Scroll area for content
        scroll_area = QScrollArea()
        scroll_area.setWidget(content_label)
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameStyle(QFrame.NoFrame)
        scroll_area.setStyleSheet("QScrollArea { background-color: transparent; }")

        content_layout.addWidget(scroll_area)
        layout.addWidget(content_frame)

        # Modern styled buttons with responsive layout
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)
        button_layout.setAlignment(Qt.AlignCenter)

        # Calculate responsive button widths
        accept_btn_width, reject_btn_width = self.calculate_responsive_widths()

        self.accept_btn = QPushButton("我已知晓并同意")
        self.accept_btn.setFixedSize(accept_btn_width, 40)
        self.accept_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 11pt;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
        """)
        self.accept_btn.clicked.connect(self.accept_disclaimer)
        button_layout.addWidget(self.accept_btn)

        self.reject_btn = QPushButton("退出程序")
        self.reject_btn.setFixedSize(reject_btn_width, 40)
        self.reject_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 11pt;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:pressed {
                background-color: #c62828;
            }
        """)
        self.reject_btn.clicked.connect(self.reject_disclaimer)
        button_layout.addWidget(self.reject_btn)

        layout.addLayout(button_layout)

        self.setLayout(layout)

    def accept_disclaimer(self):
        """
        Handle accept button click
        """
        self.accepted.emit()
        self.accept()

    def reject_disclaimer(self):
        """
        Handle reject button click - Fixed to properly terminate application
        """
        import sys
        sys.exit()  # Immediately terminate application

    def closeEvent(self, event):
        """
        Handle dialog close event - Fixed to properly terminate application
        """
        import sys
        event.accept()  # Accept the close event
        sys.exit()  # Immediately terminate application


class IPLocationDisclaimer(DisclaimerDialog):
    """
    Disclaimer for IP location functionality
    """
    def __init__(self, parent=None):
        content = ("请注意此次查询的结果仅用于辅助判断，查询结果受数据源以及其他诸多因素影响，"
                  "无法对查询结果进行任何程度的准确性保证。不得将此次查询结果作为处罚员工的依据，"
                  "也不得将此次查询结果向除你之外的任何人员共享")
        super().__init__("免责声明", content, parent)


class PhoneVerificationDisclaimer(DisclaimerDialog):
    """
    Disclaimer for phone verification functionality
    """
    def __init__(self, parent=None):
        content = ("使用人郑重承诺此次查询仅用作合规审计目的，查询的对象保证为员工本人或高度怀疑其实际控制"
                  "或代为操作的账户登记人。我清楚查询结果不得向任何人披露，更不得传输至境外，"
                  "否则将被追究法律责任")
        super().__init__("免责声明", content, parent)
