"""
Network utilities for API calls and webhook notifications
"""
import requests
import json
import uuid
import urllib.parse
import urllib3
import ssl
from PyQt5.QtCore import QObject, pyqtSignal


def get_mac_address():
    """
    Get device MAC address
    """
    try:
        mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                       for elements in range(0, 2*6, 2)][::-1])
        return mac
    except Exception as e:
        print(f"Error getting MAC address: {e}")
        return "Unknown"


class NetworkManager(QObject):
    """
    Network manager for handling API calls and webhooks
    """
    webhook_sent = pyqtSignal(bool, str)  # success, message
    api_response = pyqtSignal(bool, dict, str)  # success, data, error_message
    
    def __init__(self):
        super().__init__()
        self.webhook_url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=1ffcc61c-efa7-44ca-bacb-6312ec55da29"
    
    def send_webhook(self, message):
        """
        Send webhook notification
        """
        try:
            payload = {
                "msgtype": "text",
                "text": {
                    "content": message
                }
            }
            
            response = requests.post(
                self.webhook_url,
                json=payload,
                timeout=10,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                self.webhook_sent.emit(True, "推送成功")
            else:
                error_msg = f"推送失败，状态码: {response.status_code}"
                self.webhook_sent.emit(False, error_msg)
                
        except Exception as e:
            error_msg = f"推送失败: {str(e)}"
            self.webhook_sent.emit(False, error_msg)
    
    def query_ip_location(self, ip_address):
        """
        Query IP location using the specified API
        """
        try:
            url = f"https://ip.ll.sd/_api/all-location?ip={ip_address}"
            headers = {
                'Sec-Ch-Ua': '"Not.A/Brand";v="99", "Chromium";v="136"',
                'Sec-Ch-Ua-Mobile': '?0',
                'Sec-Ch-Ua-Platform': '"Windows"',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Upgrade-Insecure-Requests': '1',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-User': '?1',
                'Sec-Fetch-Dest': 'document',
                'Accept-Encoding': 'gzip, deflate, br',
                'Priority': 'u=0, i'
            }
            
            response = requests.get(url, headers=headers, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                self.api_response.emit(True, data, "")
            else:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                self.api_response.emit(False, {}, error_msg)
                
        except Exception as e:
            error_msg = f"请求失败: {str(e)}"
            self.api_response.emit(False, {}, error_msg)
    
    def check_mobile_status(self, mobile):
        """
        Check mobile phone status
        """
        try:
            url = 'https://kzmstatev1.market.alicloudapi.com/api-mall/api/mobile_status/check'
            appcode = '0059c700e9344f88946e4eda9e5c3481'
            
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                'Authorization': 'APPCODE ' + appcode
            }
            
            data = {'mobile': mobile}
            post_data = urllib.parse.urlencode(data).encode('utf-8')
            
            http = urllib3.PoolManager()
            response = http.request('POST', url, body=post_data, headers=headers)
            content = response.data.decode('utf-8')
            
            if response.status == 200:
                result = json.loads(content)
                self.api_response.emit(True, result, "")
            else:
                error_msg = f"HTTP {response.status}: {content}"
                self.api_response.emit(False, {}, error_msg)
                
        except Exception as e:
            error_msg = f"请求失败: {str(e)}"
            self.api_response.emit(False, {}, error_msg)
    
    def verify_mobile_identity(self, mobile, name):
        """
        Verify mobile phone identity
        """
        try:
            url = 'https://mobiletwo.shumaidata.com/mobiletwo'
            appcode = '0059c700e9344f88946e4eda9e5c3481'
            
            params = {
                'mobile': mobile,
                'name': name
            }
            
            headers = {
                'Authorization': 'APPCODE ' + appcode
            }
            
            response = requests.get(url, params=params, headers=headers, timeout=15)
            
            if response.status_code == 200:
                result = response.json()
                self.api_response.emit(True, result, "")
            else:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                self.api_response.emit(False, {}, error_msg)
                
        except Exception as e:
            error_msg = f"请求失败: {str(e)}"
            self.api_response.emit(False, {}, error_msg)
