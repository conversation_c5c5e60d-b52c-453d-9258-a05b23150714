"""
IP Location Query Tab
"""
import time
from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QPushButton, QTextEdit, QDateEdit,
                            QMessageBox)
from PyQt5.QtCore import QThread, pyqtSignal, QDate
from PyQt5.QtGui import QFont
from utils.validation_utils import validate_ip_address
from utils.network_utils import NetworkManager


class IPLocationWorker(QThread):
    """
    Worker thread for IP location queries
    """
    result_ready = pyqtSignal(bool, dict, str)  # success, data, error
    webhook_sent = pyqtSignal(bool, str)  # success, message

    def __init__(self):
        super().__init__()
        self.network_manager = NetworkManager()
        self.network_manager.api_response.connect(self.result_ready)
        self.network_manager.webhook_sent.connect(self.webhook_sent)

        self.query_ip = None
        self.webhook_message = None
        self.operation = None  # 'query' or 'webhook'

    def query_location(self, ip_address):
        """
        Query IP location
        """
        self.query_ip = ip_address
        self.operation = 'query'
        self.start()

    def send_webhook_request(self, message):
        """
        Send webhook request
        """
        self.webhook_message = message
        self.operation = 'webhook'
        self.start()

    def run(self):
        """
        Thread execution
        """
        if self.operation == 'query':
            self.network_manager.query_ip_location(self.query_ip)
        elif self.operation == 'webhook':
            self.network_manager.send_webhook(self.webhook_message)


class IPLocationTab(QWidget):
    """
    IP Location Query Tab Widget
    """
    def __init__(self, username, parent=None):
        super().__init__(parent)
        self.username = username
        self.last_query_time = 0
        self.worker = IPLocationWorker()

        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        """
        Setup the user interface
        """
        layout = QVBoxLayout()

        # Input section
        input_layout = QHBoxLayout()

        # IP address input
        ip_label = QLabel("请输入IP地址:")
        self.ip_input = QLineEdit()
        self.ip_input.setPlaceholderText("例如: *********** 或 2001:db8::1")
        input_layout.addWidget(ip_label)
        input_layout.addWidget(self.ip_input)

        # Date selection
        date_label = QLabel("请选择查询日期:")
        self.date_input = QDateEdit()
        self.date_input.setDate(QDate.currentDate())
        self.date_input.setCalendarPopup(True)
        input_layout.addWidget(date_label)
        input_layout.addWidget(self.date_input)

        # Query button
        self.query_btn = QPushButton("执行查询")
        self.query_btn.clicked.connect(self.execute_query)
        input_layout.addWidget(self.query_btn)

        layout.addLayout(input_layout)

        # Results section with updated header
        results_label = QLabel("查询结果（仅供辅助判断，不具备法律效力）:")
        results_font = QFont()
        results_font.setBold(True)
        results_label.setFont(results_font)
        results_label.setStyleSheet("color: #d32f2f; font-size: 12pt;")
        layout.addWidget(results_label)

        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setPlaceholderText("查询结果将在此显示...")
        self.results_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #ddd;
                border-radius: 6px;
                padding: 10px;
                font-size: 11pt;
                line-height: 1.5;
                background-color: #fafafa;
            }
        """)
        layout.addWidget(self.results_text)

        # Permanent disclaimer text below results
        disclaimer_text = QLabel(
            "程序使用人已承诺此次查询的结果仅用于辅助判断，并清晰知道查询结果受数据源以及其他诸多因素影响，"
            "查询结果可能不准确。此次查询结果不会被作为处罚或开除员工的依据，也不会将此次查询结果向任何人员共享"
        )
        disclaimer_text.setWordWrap(True)
        disclaimer_text.setStyleSheet("""
            QLabel {
                color: #666;
                font-size: 10pt;
                padding: 12px;
                background-color: #f0f8ff;
                border: 1px solid #b3d9ff;
                border-radius: 6px;
                margin: 5px 0px;
            }
        """)
        layout.addWidget(disclaimer_text)

        # Enhanced audit request button
        self.audit_btn = QPushButton("未查询到有效结果？点我将查询请求推送至审计部门")
        self.audit_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 12px 20px;
                font-size: 11pt;
                font-weight: bold;
                margin: 10px 0px;
            }
            QPushButton:hover {
                background-color: #f57c00;
            }
            QPushButton:pressed {
                background-color: #ef6c00;
            }
        """)
        self.audit_btn.clicked.connect(self.request_audit)
        layout.addWidget(self.audit_btn)

        self.setLayout(layout)

    def connect_signals(self):
        """
        Connect worker signals
        """
        self.worker.result_ready.connect(self.handle_query_result)
        self.worker.webhook_sent.connect(self.handle_webhook_result)

    def execute_query(self):
        """
        Execute IP location query - Fixed to show disclaimer before query
        """
        # Validate IP address first
        ip_address = self.ip_input.text().strip()
        if not ip_address:
            QMessageBox.warning(self, "输入错误", "请输入IP地址")
            return

        if not validate_ip_address(ip_address):
            QMessageBox.warning(self, "输入错误", "请输入有效的IPv4或IPv6地址")
            return

        # Show disclaimer dialog before proceeding
        from ui.disclaimer_dialog import IPLocationDisclaimer
        disclaimer = IPLocationDisclaimer(self)
        disclaimer.accepted.connect(lambda: self.proceed_with_query(ip_address))
        disclaimer.exec_()

    def proceed_with_query(self, ip_address):
        """
        Proceed with the actual query after disclaimer acceptance
        """
        # Check rate limiting (1 minute interval)
        current_time = time.time()
        if current_time - self.last_query_time < 60:
            remaining = 60 - (current_time - self.last_query_time)
            QMessageBox.warning(self, "查询限制",
                              f"请等待 {int(remaining)} 秒后再进行下一次查询")
            return

        # Disable button and start query
        self.query_btn.setEnabled(False)
        self.query_btn.setText("查询中...")
        self.results_text.clear()
        self.results_text.append("正在查询，请稍候...")

        # Update last query time
        self.last_query_time = current_time

        # Start worker
        self.worker.query_location(ip_address)

    def handle_query_result(self, success, data, error_message):
        """
        Handle query result from worker
        """
        self.query_btn.setEnabled(True)
        self.query_btn.setText("执行查询")

        if success and data.get('success'):
            # Parse successful response with improved error handling
            locations = data.get('locations', {})

            result_text = "查询结果:\n\n"

            # Handle incomplete location data - only show available addresses
            location_fields = [
                ('locationA', '命中地址1'),
                ('locationB', '命中地址2'),
                ('recommend', '命中地址3'),
                ('standard_address', '命中地址4')
            ]

            available_count = 0
            for field_name, display_name in location_fields:
                location_value = locations.get(field_name)
                if location_value and location_value.strip():
                    result_text += f"{display_name}: {location_value}\n"
                    available_count += 1

            if available_count == 0:
                result_text += "未获取到有效的地址信息\n"

            # Fixed: Replace coordinate parsing with static text
            result_text += "\n坐标信息：已隐藏，确需查看的请推送至审计部门查询"

            self.results_text.clear()
            self.results_text.setPlainText(result_text)
        else:
            # Handle failure
            self.results_text.clear()
            self.results_text.append("查询失败，请通知审计部门进行维护")

            # Log detailed error to console
            if error_message:
                print(f"IP查询错误: {error_message}")
            if data:
                print(f"响应数据: {data}")

    def request_audit(self):
        """
        Request audit department assistance
        """
        ip_address = self.ip_input.text().strip()
        query_date = self.date_input.date().toString("yyyy-MM-dd")

        if not ip_address:
            QMessageBox.warning(self, "输入错误", "请先输入IP地址")
            return

        # Prepare webhook message with corrected format (added space)
        message = f"{self.username}请求查询{query_date}的{ip_address} IP地址物理位置"

        # Send webhook
        self.worker.send_webhook_request(message)

    def handle_webhook_result(self, success, message):
        """
        Handle webhook result
        """
        if success:
            QMessageBox.information(self, "推送成功", "查询请求已成功推送至审计部门")
        else:
            QMessageBox.critical(self, "推送失败", f"推送失败: {message}")
