"""
Main application window with three independent tabs
"""
from PyQt5.QtWidgets import (QMainWindow, QTabWidget, QWidget, QVBoxLayout)
from PyQt5.QtCore import QThread, pyqtSignal
from utils.resource_utils import get_application_icon
from utils.network_utils import get_mac_address, NetworkManager
from tabs.ip_location_tab import IPLocationTab
from tabs.phone_verification_tab import PhoneVerificationTab
from tabs.document_verification_tab import DocumentVerificationTab


class StartupWorker(QThread):
    """
    Worker thread for startup webhook notification
    """
    webhook_sent = pyqtSignal(bool, str)  # success, message

    def __init__(self, username):
        super().__init__()
        self.username = username
        self.network_manager = NetworkManager()
        self.network_manager.webhook_sent.connect(self.webhook_sent)

    def run(self):
        """
        Send startup notification
        """
        mac_address = get_mac_address()
        message = f"使用人{self.username}，设备MAC地址{mac_address}"
        self.network_manager.send_webhook(message)


class MainWindow(QMainWindow):
    """
    Main application window
    """
    def __init__(self, username):
        super().__init__()
        self.username = username
        self.startup_worker = StartupWorker(username)

        self.setWindowTitle("违规炒股风险排查")
        self.setGeometry(100, 100, 1000, 700)

        # Set application icon using base64 data with file fallback
        try:
            icon = get_application_icon()
            if icon is not None:
                self.setWindowIcon(icon)
            else:
                print("Warning: Could not load window icon")
        except Exception as e:
            print(f"Failed to load icon: {e}")

        self.setup_ui()
        self.send_startup_notification()

        # Fixed: Do NOT show disclaimer on startup - it should only appear when user clicks "执行查询"

    def setup_ui(self):
        """
        Setup the user interface
        """
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout()
        central_widget.setLayout(layout)

        # Create tab widget
        self.tab_widget = QTabWidget()

        # Create tabs (independent instances)
        self.ip_tab = IPLocationTab(self.username)
        self.phone_tab = PhoneVerificationTab(self.username)
        self.document_tab = DocumentVerificationTab(self.username)

        # Add tabs
        self.tab_widget.addTab(self.ip_tab, "IP地址定位查询")
        self.tab_widget.addTab(self.phone_tab, "机主实名信息查询")
        self.tab_widget.addTab(self.document_tab, "虚假材料取证")

        # Set default tab (IP location)
        self.tab_widget.setCurrentIndex(0)

        layout.addWidget(self.tab_widget)

    def send_startup_notification(self):
        """
        Send startup notification webhook
        """
        self.startup_worker.webhook_sent.connect(self.handle_startup_notification)
        self.startup_worker.start()

    def handle_startup_notification(self, success, message):
        """
        Handle startup notification result
        """
        if not success:
            print(f"启动通知发送失败: {message}")
        # Note: We don't show error to user for startup notification

    def closeEvent(self, event):
        """
        Handle window close event
        """
        # Clean up any running threads
        if hasattr(self, 'startup_worker') and self.startup_worker.isRunning():
            self.startup_worker.terminate()
            self.startup_worker.wait()

        # Clean up tab workers
        if hasattr(self.ip_tab, 'worker') and self.ip_tab.worker.isRunning():
            self.ip_tab.worker.terminate()
            self.ip_tab.worker.wait()

        if hasattr(self.phone_tab, 'worker') and self.phone_tab.worker.isRunning():
            self.phone_tab.worker.terminate()
            self.phone_tab.worker.wait()

        event.accept()
