"""
Document Verification Tab
"""
import os
import random
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QComboBox, QPushButton, QFileDialog,
                            QMessageBox, QSpacerItem, QSizePolicy, QScrollArea)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QPixmap
from PIL import Image
import openpyxl
import PyPDF2


class DocumentPreviewWidget(QWidget):
    """
    Widget for document preview
    """
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()

    def setup_ui(self):
        """
        Setup the preview UI
        """
        layout = QVBoxLayout()

        # Preview label
        preview_label = QLabel("文件预览:")
        preview_font = QFont()
        preview_font.setBold(True)
        preview_label.setFont(preview_font)
        layout.addWidget(preview_label)

        # Scroll area for preview
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setMinimumHeight(200)

        self.preview_content = QLabel("未选择文件")
        self.preview_content.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        self.preview_content.setWordWrap(True)
        self.preview_content.setStyleSheet("padding: 10px; background-color: #f9f9f9; border: 1px solid #ddd;")

        self.scroll_area.setWidget(self.preview_content)
        layout.addWidget(self.scroll_area)

        self.setLayout(layout)

    def preview_file(self, file_path):
        """
        Preview file content
        """
        try:
            file_ext = os.path.splitext(file_path)[1].lower()

            if file_ext in ['.png', '.jpg', '.jpeg']:
                self.preview_image(file_path)
            elif file_ext == '.pdf':
                self.preview_pdf(file_path)
            elif file_ext in ['.xls', '.xlsx']:
                self.preview_excel(file_path)
            else:
                self.preview_content.setText("不支持的文件格式")

        except Exception as e:
            self.preview_content.setText(f"预览失败: {str(e)}")

    def preview_image(self, file_path):
        """
        Preview image file
        """
        try:
            # Load and resize image
            image = Image.open(file_path)

            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Resize for preview (max 400x300)
            image.thumbnail((400, 300), Image.Resampling.LANCZOS)

            # Save temporary file
            temp_path = "temp_preview.png"
            image.save(temp_path)

            # Display in label
            pixmap = QPixmap(temp_path)
            self.preview_content.setPixmap(pixmap)
            self.preview_content.setText("")

            # Clean up
            if os.path.exists(temp_path):
                os.remove(temp_path)

        except Exception as e:
            self.preview_content.setText(f"图片预览失败: {str(e)}")

    def preview_pdf(self, file_path):
        """
        Preview PDF file with improved handling for images and text
        """
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)

                if len(pdf_reader.pages) > 0:
                    first_page = pdf_reader.pages[0]
                    text = first_page.extract_text()

                    preview_text = f"PDF文件预览 (第1页，共{len(pdf_reader.pages)}页):\n\n"

                    # Check if text extraction was successful
                    if text and text.strip():
                        preview_text += text[:800]  # Increased character limit
                        if len(text) > 800:
                            preview_text += "\n\n... (内容已截断)"
                    else:
                        # If no text found, likely contains images or scanned content
                        preview_text += "此文件包含图像、表格内容或扫描文档。\n"
                        preview_text += "文件特征码已被记录。\n"
                        preview_text += "点击执行检查后，将上传此文件至服务器进行检查。"

                    # Try to detect if PDF contains images
                    try:
                        if '/XObject' in first_page.get('/Resources', {}):
                            preview_text += "\n\n📷 检测到此PDF包含图像内容"
                    except:
                        pass

                    self.preview_content.setPixmap(QPixmap())  # Clear any image
                    self.preview_content.setText(preview_text)
                else:
                    self.preview_content.setText("PDF文件为空")

        except Exception as e:
            self.preview_content.setText(f"PDF预览失败: {str(e)}\n\n可能原因：文件损坏、加密保护或格式不兼容")

    def preview_excel(self, file_path):
        """
        Preview Excel file (first few rows)
        """
        try:
            workbook = openpyxl.load_workbook(file_path)
            sheet = workbook.active

            preview_text = f"Excel文件预览 (工作表: {sheet.title}):\n\n"

            # Get first 5 rows and 5 columns
            max_rows = min(5, sheet.max_row)
            max_cols = min(5, sheet.max_column)

            for row in range(1, max_rows + 1):
                row_data = []
                for col in range(1, max_cols + 1):
                    cell_value = sheet.cell(row=row, column=col).value
                    if cell_value is None:
                        cell_value = ""
                    row_data.append(str(cell_value)[:20])  # Limit cell content

                preview_text += " | ".join(row_data) + "\n"

            if sheet.max_row > 5 or sheet.max_column > 5:
                preview_text += "\n... (内容已截断)"

            self.preview_content.setPixmap(QPixmap())  # Clear any image
            self.preview_content.setText(preview_text)

        except Exception as e:
            self.preview_content.setText(f"Excel预览失败: {str(e)}")


class DocumentVerificationTab(QWidget):
    """
    Document Verification Tab Widget
    """
    def __init__(self, username, parent=None):
        super().__init__(parent)
        self.username = username
        self.selected_file = None

        self.setup_ui()

    def setup_ui(self):
        """
        Setup the user interface
        """
        layout = QVBoxLayout()

        # Selection section
        selection_layout = QHBoxLayout()

        # Content type selection
        type_label = QLabel("请选择核验内容:")
        self.type_combo = QComboBox()
        self.type_combo.addItems([
            "请选择...",
            "电子对账单",
            "纸质对账单",
            "一证通查短信截图",
            "利害关系人登记信息表",
            "利害关系人登记信息截图"
        ])

        selection_layout.addWidget(type_label)
        selection_layout.addWidget(self.type_combo)
        selection_layout.addStretch()

        layout.addLayout(selection_layout)

        # File upload section
        upload_layout = QHBoxLayout()

        self.file_label = QLabel("未选择文件")
        self.file_label.setStyleSheet("padding: 10px; background-color: #f0f0f0; border: 1px solid #ccc; border-radius: 4px;")

        self.upload_btn = QPushButton("选择文件")
        self.upload_btn.clicked.connect(self.select_file)

        upload_layout.addWidget(self.file_label, 1)
        upload_layout.addWidget(self.upload_btn)

        layout.addLayout(upload_layout)

        # Preview section with increased height
        self.preview_widget = DocumentPreviewWidget()
        self.preview_widget.setMinimumHeight(400)  # Increased height to better utilize space
        layout.addWidget(self.preview_widget)

        # Check button
        self.check_btn = QPushButton("执行检查")
        self.check_btn.clicked.connect(self.execute_check)
        layout.addWidget(self.check_btn)

        # Reduced bottom spacing
        layout.addItem(QSpacerItem(20, 10, QSizePolicy.Minimum, QSizePolicy.Fixed))

        self.setLayout(layout)

    def select_file(self):
        """
        Select file for upload
        """
        file_dialog = QFileDialog()
        file_path, _ = file_dialog.getOpenFileName(
            self,
            "选择文件",
            "",
            "支持的文件 (*.pdf *.xls *.xlsx *.png *.jpg *.jpeg);;PDF文件 (*.pdf);;Excel文件 (*.xls *.xlsx);;图片文件 (*.png *.jpg *.jpeg)"
        )

        if file_path:
            self.selected_file = file_path
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)

            # Format file size
            if file_size < 1024:
                size_str = f"{file_size} B"
            elif file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.1f} KB"
            else:
                size_str = f"{file_size / (1024 * 1024):.1f} MB"

            self.file_label.setText(f"已选择: {file_name} ({size_str})")

            # Preview the file
            self.preview_widget.preview_file(file_path)

    def execute_check(self):
        """
        Execute document check
        """
        # Validate inputs
        if self.type_combo.currentIndex() == 0:
            QMessageBox.warning(self, "选择错误", "请选择核验内容")
            return

        if not self.selected_file:
            QMessageBox.warning(self, "文件错误", "请选择要检查的文件")
            return

        # Disable button
        self.check_btn.setEnabled(False)
        self.check_btn.setText("检查中...")

        # Random delay (2-5 seconds)
        delay = random.randint(2000, 5000)  # milliseconds

        # Use QTimer for delay
        QTimer.singleShot(delay, self.show_warning_dialog)

    def show_warning_dialog(self):
        """
        Show warning dialog after delay
        """
        # Re-enable button
        self.check_btn.setEnabled(True)
        self.check_btn.setText("执行检查")

        # Show warning dialog
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("警告")
        msg_box.setText("敏感功能仅限审计内部使用，如您是审计人员请使用已登记的设备")
        msg_box.setIcon(QMessageBox.Warning)

        close_btn = msg_box.addButton("关闭", QMessageBox.AcceptRole)
        msg_box.setDefaultButton(close_btn)

        msg_box.exec_()
