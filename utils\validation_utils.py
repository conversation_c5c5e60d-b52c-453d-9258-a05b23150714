"""
Input validation utilities
"""
import re
import ipaddress


def validate_ip_address(ip_str):
    """
    Validate IPv4 or IPv6 address
    """
    try:
        ipaddress.ip_address(ip_str)
        return True
    except ValueError:
        return False


def validate_phone_number(phone_str):
    """
    Validate Chinese mobile phone number (11 digits)
    """
    if not phone_str:
        return False
    
    # Remove any spaces or dashes
    phone_clean = re.sub(r'[\s-]', '', phone_str)
    
    # Check if it's exactly 11 digits
    if len(phone_clean) != 11:
        return False
    
    # Check if all characters are digits
    if not phone_clean.isdigit():
        return False
    
    # Check if it starts with 1 (Chinese mobile numbers start with 1)
    if not phone_clean.startswith('1'):
        return False
    
    return True


def validate_chinese_name(name_str):
    """
    Validate Chinese name (at least 2 Chinese characters)
    """
    if not name_str:
        return False
    
    # Remove whitespace
    name_clean = name_str.strip()
    
    # Check if it has at least 2 characters
    if len(name_clean) < 2:
        return False
    
    # Check if all characters are Chinese characters
    chinese_pattern = re.compile(r'^[\u4e00-\u9fff]+$')
    return bool(chinese_pattern.match(name_clean))


def validate_username(username):
    """
    Validate if username is authorized (巩海燕 or 梁誉)
    """
    authorized_users = ["巩海燕", "梁誉"]
    return username.strip() in authorized_users
