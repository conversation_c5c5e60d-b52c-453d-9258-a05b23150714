"""
Main application entry point for 违规炒股风险排查 (Illegal Stock Trading Risk Investigation)
"""
import sys
import os
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import Qt

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.auth_dialog import AuthDialog
from ui.main_window import MainWindow
from utils.resource_utils import get_application_icon


class Application:
    """
    Main application class
    """
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.app.setApplicationName("违规炒股风险排查")
        self.app.setApplicationVersion("1.0")

        # Set application icon using base64 data with file fallback
        try:
            icon = get_application_icon()
            if icon is not None:
                self.app.setWindowIcon(icon)
            else:
                print("Warning: Could not load application icon")
        except Exception as e:
            print(f"Failed to load application icon: {e}")

        # Set application style
        self.app.setStyle('Fusion')

        self.main_window = None
        self.username = None

    def run(self):
        """
        Run the application
        """
        # Show authentication dialog
        if self.authenticate():
            # Authentication successful, show main window
            self.show_main_window()
            return self.app.exec_()
        else:
            # Authentication failed or cancelled
            return 0

    def authenticate(self):
        """
        Handle user authentication - Fixed to prevent duplicate error dialogs
        """
        auth_dialog = AuthDialog()
        auth_dialog.authenticated.connect(self.on_authentication_success)

        result = auth_dialog.exec_()

        if result == auth_dialog.Accepted and self.username:
            return True
        else:
            # Fixed: Do not show additional error dialog here since AuthDialog handles it
            return False

    def on_authentication_success(self, username):
        """
        Handle successful authentication
        """
        self.username = username

    def show_main_window(self):
        """
        Show the main application window
        """
        self.main_window = MainWindow(self.username)
        self.main_window.show()


def main():
    """
    Application entry point
    """
    # Enable high DPI scaling
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    try:
        app = Application()
        sys.exit(app.run())
    except Exception as e:
        print(f"Application error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
