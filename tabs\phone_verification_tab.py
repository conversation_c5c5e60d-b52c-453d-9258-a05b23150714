"""
Phone Verification Tab
"""
import time
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                            QLineEdit, QPushButton, QTextEdit, QMessageBox,
                            QSpacerItem, QSizePolicy)
from PyQt5.QtCore import QThread, pyqtSignal
from PyQt5.QtGui import QFont
from utils.validation_utils import validate_phone_number, validate_chinese_name
from utils.network_utils import NetworkManager
from ui.disclaimer_dialog import PhoneVerificationDisclaimer


class PhoneVerificationWorker(QThread):
    """
    Worker thread for phone verification queries
    """
    result_ready = pyqtSignal(bool, dict, str)  # success, data, error

    def __init__(self):
        super().__init__()
        self.network_manager = NetworkManager()
        self.network_manager.api_response.connect(self.result_ready)

        self.phone_number = None
        self.name = None
        self.operation = None  # 'status' or 'verify'

    def check_status(self, phone_number):
        """
        Check phone status
        """
        self.phone_number = phone_number
        self.operation = 'status'
        self.start()

    def verify_identity(self, phone_number, name):
        """
        Verify phone identity
        """
        self.phone_number = phone_number
        self.name = name
        self.operation = 'verify'
        self.start()

    def run(self):
        """
        Thread execution
        """
        if self.operation == 'status':
            self.network_manager.check_mobile_status(self.phone_number)
        elif self.operation == 'verify':
            self.network_manager.verify_mobile_identity(self.phone_number, self.name)


class PhoneVerificationTab(QWidget):
    """
    Phone Verification Tab Widget
    """
    def __init__(self, username, parent=None):
        super().__init__(parent)
        self.username = username
        self.last_query_time = 0
        self.worker = PhoneVerificationWorker()

        self.setup_ui()
        self.connect_signals()

    def setup_ui(self):
        """
        Setup the user interface
        """
        layout = QVBoxLayout()

        # Input section
        input_layout = QVBoxLayout()

        # Phone number input
        phone_layout = QHBoxLayout()
        phone_label = QLabel("手机号:")
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("请输入11位手机号")
        phone_layout.addWidget(phone_label)
        phone_layout.addWidget(self.phone_input)
        input_layout.addLayout(phone_layout)

        # Name input
        name_layout = QHBoxLayout()
        name_label = QLabel("姓名:")
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("请输入中文姓名")
        name_layout.addWidget(name_label)
        name_layout.addWidget(self.name_input)
        input_layout.addLayout(name_layout)

        layout.addLayout(input_layout)

        # Button section
        button_layout = QHBoxLayout()

        self.status_btn = QPushButton("查询状态")
        self.status_btn.clicked.connect(self.check_status)
        button_layout.addWidget(self.status_btn)

        self.verify_btn = QPushButton("验证实名信息")
        self.verify_btn.clicked.connect(self.verify_identity)
        button_layout.addWidget(self.verify_btn)

        layout.addLayout(button_layout)

        # Results section
        results_label = QLabel("查询结果:")
        results_font = QFont()
        results_font.setBold(True)
        results_label.setFont(results_font)
        layout.addWidget(results_label)

        self.results_text = QTextEdit()
        self.results_text.setReadOnly(True)
        self.results_text.setPlaceholderText("查询结果将在此显示...")
        layout.addWidget(self.results_text)

        # Spacer
        layout.addItem(QSpacerItem(20, 20, QSizePolicy.Minimum, QSizePolicy.Expanding))

        # Enhanced disclaimer text with better visual impact
        disclaimer_label = QLabel("⚠️ 重要提醒：程序使用人已郑重承诺此次查询仅用作合规审计目的，查询的对象保证为员工本人或高度怀疑其实际控制或代为操作的账户登记人。使用人清楚查询结果不得向任何人披露，更不得传输至境外，否则将被追究法律责任")
        disclaimer_label.setWordWrap(True)
        disclaimer_label.setStyleSheet("""
            QLabel {
                color: #d32f2f;
                font-size: 12pt;
                font-weight: bold;
                padding: 15px;
                background-color: #fff3e0;
                border: 2px solid #ff9800;
                border-radius: 8px;
                margin: 10px 0px;
            }
        """)
        layout.addWidget(disclaimer_label)

        self.setLayout(layout)

    def connect_signals(self):
        """
        Connect worker signals
        """
        self.worker.result_ready.connect(self.handle_query_result)

    def validate_inputs(self):
        """
        Validate user inputs
        """
        phone = self.phone_input.text().strip()
        name = self.name_input.text().strip()

        if not phone:
            QMessageBox.warning(self, "输入错误", "请输入手机号")
            return False

        if not validate_phone_number(phone):
            QMessageBox.warning(self, "输入错误", "请输入有效的11位手机号")
            return False

        if not name:
            QMessageBox.warning(self, "输入错误", "请输入姓名")
            return False

        if not validate_chinese_name(name):
            QMessageBox.warning(self, "输入错误", "请输入有效的中文姓名（至少2个汉字）")
            return False

        return True

    def check_rate_limit(self):
        """
        Check rate limiting (2 minutes interval)
        """
        current_time = time.time()
        if current_time - self.last_query_time < 120:
            remaining = 120 - (current_time - self.last_query_time)
            QMessageBox.warning(self, "查询限制",
                              f"请等待 {int(remaining)} 秒后再进行下一次查询")
            return False

        self.last_query_time = current_time
        return True

    def show_disclaimer_and_proceed(self, callback):
        """
        Show disclaimer dialog and proceed if accepted
        """
        disclaimer = PhoneVerificationDisclaimer(self)
        disclaimer.accepted.connect(callback)
        disclaimer.rejected.connect(self.close_application)
        disclaimer.exec_()

    def close_application(self):
        """
        Close the entire application
        """
        import sys
        sys.exit()

    def check_status(self):
        """
        Check phone status
        """
        if not self.validate_inputs():
            return

        if not self.check_rate_limit():
            return

        def proceed():
            self.status_btn.setEnabled(False)
            self.verify_btn.setEnabled(False)
            self.status_btn.setText("查询中...")
            self.results_text.clear()
            self.results_text.append("正在查询手机号状态，请稍候...")

            phone = self.phone_input.text().strip()
            self.worker.check_status(phone)

        self.show_disclaimer_and_proceed(proceed)

    def verify_identity(self):
        """
        Verify phone identity
        """
        if not self.validate_inputs():
            return

        if not self.check_rate_limit():
            return

        def proceed():
            self.status_btn.setEnabled(False)
            self.verify_btn.setEnabled(False)
            self.verify_btn.setText("验证中...")
            self.results_text.clear()
            self.results_text.append("正在验证实名信息，请稍候...")

            phone = self.phone_input.text().strip()
            name = self.name_input.text().strip()
            self.worker.verify_identity(phone, name)

        self.show_disclaimer_and_proceed(proceed)

    def handle_query_result(self, success, data, error_message):
        """
        Handle query result from worker
        """
        self.status_btn.setEnabled(True)
        self.verify_btn.setEnabled(True)
        self.status_btn.setText("查询状态")
        self.verify_btn.setText("验证实名信息")

        if success and data.get('success'):
            if self.worker.operation == 'status':
                self.handle_status_result(data)
            elif self.worker.operation == 'verify':
                self.handle_verify_result(data)
        else:
            # Handle failure
            self.results_text.clear()
            self.results_text.append("查询被拒绝，请联系审计人员")

            # Log detailed error to console
            if error_message:
                print(f"手机查询错误: {error_message}")
            if data:
                print(f"响应数据: {data}")

    def handle_status_result(self, data):
        """
        Handle phone status result
        """
        phone = self.phone_input.text().strip()
        result_data = data.get('data', {})

        result_code = result_data.get('result', '')
        channel = result_data.get('channel', '未知')
        transfer_status = result_data.get('transferStatus', '0')

        # Map result codes to descriptions
        status_map = {
            '0': '销号或未启用(不在网)',
            '1': '正常',
            '2': '停机',
            '3': '在网但不可用',
            '4': '预销号',
            '5': '关机'
        }

        status_desc = status_map.get(result_code, '未知状态')
        transfer_desc = '已转网' if transfer_status == '1' else '未转网'

        result_text = f"查询的号码 {phone} {status_desc}，{transfer_desc}\n"
        result_text += f"运营商: {channel}"

        self.results_text.clear()
        self.results_text.append(result_text)

    def handle_verify_result(self, data):
        """
        Handle identity verification result
        """
        result_data = data.get('data', {})
        result_code = result_data.get('result', '')

        if result_code == 0:
            result_text = "手机号及姓名匹配一致"
        else:
            result_text = "<b><font color='red'>手机号及姓名匹配不一致</font></b>"

        self.results_text.clear()
        self.results_text.setHtml(result_text)
