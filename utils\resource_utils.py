"""
Resource utilities for proper path handling in packaged applications
"""
import os
import sys
import base64
from PyQt5.QtGui import QIcon, QPixmap
from PyQt5.QtCore import Qt

# Base64 encoded icon data - placeholder (to be filled with actual icon data)
# This will be populated with the actual base64 encoded icon data
ICON_BASE64_DATA = "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"

def get_resource_path(relative_path):
    """
    Get absolute path to resource, works for dev and for PyInstaller
    """
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.abspath(".")

    return os.path.join(base_path, relative_path)


def get_icon_path():
    """
    Get the path to the application icon (fallback method)
    """
    return get_resource_path("resources/icon.ico")


def create_icon_from_base64(base64_data=None):
    """
    Create QIcon from base64 encoded data

    Args:
        base64_data (str): Base64 encoded icon data. If None, uses ICON_BASE64_DATA

    Returns:
        QIcon: Created icon object, or None if creation failed
    """
    if base64_data is None:
        base64_data = ICON_BASE64_DATA

    if not base64_data or base64_data.strip() == "":
        return None

    try:
        # Decode base64 data
        icon_bytes = base64.b64decode(base64_data)

        # Create QPixmap from bytes
        pixmap = QPixmap()
        if pixmap.loadFromData(icon_bytes):
            # Create icon with multiple sizes for better compatibility
            icon = QIcon()
            for size in [16, 24, 32, 48, 64]:
                scaled_pixmap = pixmap.scaled(size, size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                icon.addPixmap(scaled_pixmap)

            return icon if not icon.isNull() else None

    except Exception as e:
        print(f"Failed to create icon from base64 data: {e}")

    return None


def get_application_icon():
    """
    Get application icon using base64 data with file fallback

    Returns:
        QIcon: Application icon or None if all methods fail
    """
    # Method 1: Try base64 encoded icon
    icon = create_icon_from_base64()
    if icon is not None:
        return icon

    # Method 2: Fallback to file-based icon
    try:
        icon_path = get_icon_path()
        if icon_path and os.path.exists(icon_path):
            # Try loading with QIcon directly
            icon = QIcon(icon_path)
            if not icon.isNull():
                return icon

            # Try loading with QPixmap first, then QIcon
            pixmap = QPixmap(icon_path)
            if not pixmap.isNull():
                # Create icon with multiple sizes for better compatibility
                icon = QIcon()
                for size in [16, 24, 32, 48, 64]:
                    scaled_pixmap = pixmap.scaled(size, size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    icon.addPixmap(scaled_pixmap)

                if not icon.isNull():
                    return icon
    except Exception as e:
        print(f"Failed to load icon from file: {e}")

    return None
